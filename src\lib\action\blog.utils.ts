// Blog content processing types
export interface BlogContentInput {
  title: string;
  content: string;
  author?: string;
  source?: string;
  categories?: string;
  date?: string;
}

export interface BlogCreditObject {
  author: string;
  source?: string;
  date: string;
}

export interface BlogMetadataOutput {
  title: string;
  slug: string;
  credit: BlogCreditObject;
  categories: string[];
}

export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, "")
    .replace(/\s+/g, "-")
    .replace(/--+/g, "-")
    .trim();
}

export function formatDate(dateString: string | Date, options: Intl.DateTimeFormatOptions = {}): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    ...options,
  });
}

// Generate URL-safe slug from title
export function generateUrlSafeSlug(title: string): string {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

// Normalize categories from comma-separated string to array
export function normalizeCategories(categoriesInput?: string): string[] {
  if (!categoriesInput || typeof categoriesInput !== 'string') {
    return [];
  }

  return categoriesInput
    .split(',')
    .map(category => category.trim())
    .filter(category => category.length > 0)
    .map(category => category.charAt(0).toUpperCase() + category.slice(1).toLowerCase())
    .filter((category, index, array) => array.indexOf(category) === index); // Remove duplicates
}

// Create credit object with proper validation
export function createCreditObject(author?: string, source?: string, date?: string): BlogCreditObject {
  const today = new Date();
  const formattedDate = date ?
    new Date(date).toISOString().split('T')[0] :
    today.toISOString().split('T')[0];

  return {
    author: author || 'Anonymous',
    source: source && isValidUrl(source) ? source : undefined,
    date: formattedDate
  };
}

// Validate URL format
function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Main function to generate blog metadata from input data
export function generateBlogMetadata(input: BlogContentInput): BlogMetadataOutput {
  const slug = generateUrlSafeSlug(input.title);
  const categories = normalizeCategories(input.categories);
  const credit = createCreditObject(input.author, input.source, input.date);

  return {
    title: input.title.trim(),
    slug,
    credit,
    categories
  };
}