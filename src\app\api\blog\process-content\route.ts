import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { generateBlogMetadata, BlogContentInput } from "@/lib/action/blog.utils";

// Schema validation for blog content processing
const BlogContentProcessSchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  content: z.string().min(10, "Content must be at least 10 characters"),
  author: z.string().optional(),
  source: z.string().url("Source must be a valid URL").optional().or(z.literal("")),
  categories: z.string().optional(),
  date: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input data
    const validation = BlogContentProcessSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const input: BlogContentInput = {
      title: validation.data.title,
      content: validation.data.content,
      author: validation.data.author,
      source: validation.data.source || undefined,
      categories: validation.data.categories,
      date: validation.data.date,
    };

    // Generate metadata using the utility function
    const metadata = generateBlogMetadata(input);

    return NextResponse.json({
      success: true,
      data: metadata
    });

  } catch (error) {
    console.error("Blog content processing error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET endpoint for testing the API
export async function GET() {
  const exampleInput: BlogContentInput = {
    title: "The Rise of AI Tools in Everyday Life",
    content: "A deep dive into how ChatGPT, Midjourney, and others are shaping our world.",
    author: "Sarah Khan",
    source: "https://example.com/ai-rise",
    categories: "ai, future, productivity, ai",
  };

  const metadata = generateBlogMetadata(exampleInput);

  return NextResponse.json({
    success: true,
    example: {
      input: exampleInput,
      output: metadata
    }
  });
}
