# Blog Content Processing System

A comprehensive system for generating blog metadata (slug, credit, categories) from title and content, following the exact specifications from your prompt.

## 🎯 Features

- **URL-safe slug generation** from blog titles
- **Credit object creation** with author, source, and date
- **Category normalization** with deduplication and proper formatting
- **API endpoint** for server-side processing
- **React component** for real-time metadata generation
- **Integration** with existing blog editor

## 📁 File Structure

```
src/
├── lib/action/blog.utils.ts          # Core utility functions
├── app/api/blog/process-content/      # API endpoint
├── components/blog/editor/
│   ├── MetadataProcessor.tsx          # Standalone component
│   ├── EditorSidebar.tsx             # Updated with metadata processor
│   └── BlogPostEditor.tsx            # Updated to pass title/content
└── app/admin/blog/metadata-demo/     # Demo page
```

## 🔧 Core Functions

### `generateBlogMetadata(input: BlogContentInput): BlogMetadataOutput`

Main function that processes blog content and returns formatted metadata.

**Input Format:**
```typescript
interface BlogContentInput {
  title: string;
  content: string;
  author?: string;
  source?: string;
  categories?: string;
  date?: string;
}
```

**Output Format:**
```typescript
interface BlogMetadataOutput {
  title: string;
  slug: string;
  credit: {
    author: string;
    source?: string;
    date: string; // YYYY-MM-DD format
  };
  categories: string[];
}
```

### Individual Utility Functions

- `generateUrlSafeSlug(title: string)` - Creates SEO-friendly slugs
- `normalizeCategories(categories: string)` - Processes comma-separated categories
- `createCreditObject(author, source, date)` - Builds credit information

## 🌐 API Endpoint

### POST `/api/blog/process-content`

Processes blog content and returns metadata.

**Request Body:**
```json
{
  "title": "The Rise of AI Tools in Everyday Life",
  "content": "A deep dive into how ChatGPT, Midjourney, and others are shaping our world.",
  "author": "Sarah Khan",
  "source": "https://example.com/ai-rise",
  "categories": "ai, future, productivity, ai"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "title": "The Rise of AI Tools in Everyday Life",
    "slug": "the-rise-of-ai-tools-in-everyday-life",
    "credit": {
      "author": "Sarah Khan",
      "source": "https://example.com/ai-rise",
      "date": "2025-05-31"
    },
    "categories": ["Ai", "Future", "Productivity"]
  }
}
```

### GET `/api/blog/process-content`

Returns an example of the processing functionality.

## 🎨 React Components

### MetadataProcessor Component

A standalone component for generating and previewing blog metadata.

**Props:**
```typescript
interface MetadataProcessorProps {
  title: string;
  content: string;
  onMetadataGenerated?: (metadata: BlogMetadataOutput) => void;
  onSlugChange?: (slug: string) => void;
  onCategoriesChange?: (categories: string[]) => void;
  className?: string;
}
```

**Features:**
- Real-time metadata generation
- Copy-to-clipboard functionality
- Manual input fields for author/source/categories
- JSON output preview
- API processing option

### Integration with Blog Editor

The component is integrated into the `EditorSidebar` and automatically generates metadata when title and content are available.

## 🚀 Usage Examples

### Basic Usage

```typescript
import { generateBlogMetadata } from '@/lib/action/blog.utils';

const input = {
  title: "The Rise of AI Tools in Everyday Life",
  content: "A deep dive into how ChatGPT, Midjourney, and others are shaping our world.",
  author: "Sarah Khan",
  source: "https://example.com/ai-rise",
  categories: "ai, future, productivity, ai"
};

const metadata = generateBlogMetadata(input);
console.log(metadata);
```

### API Usage

```javascript
const response = await fetch('/api/blog/process-content', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(input)
});

const result = await response.json();
if (result.success) {
  console.log(result.data);
}
```

### Component Usage

```tsx
import { MetadataProcessor } from '@/components/blog/editor/MetadataProcessor';

<MetadataProcessor
  title={blogTitle}
  content={blogContent}
  onMetadataGenerated={(metadata) => {
    setSlug(metadata.slug);
    setCategories(metadata.categories);
  }}
/>
```

## 🧪 Testing

### Demo Page

Visit `/admin/blog/metadata-demo` to test the functionality with:
- Pre-filled examples
- Real-time processing
- API testing
- Component demonstration

### Test Cases Covered

1. **Example from prompt** - Exact match with expected output
2. **Special characters** - Proper slug generation with symbols
3. **Missing optional fields** - Graceful handling of empty values
4. **Invalid URLs** - Source validation and filtering
5. **Duplicate categories** - Deduplication and normalization

## 🔍 Processing Rules

### Slug Generation
- Convert to lowercase
- Remove special characters (keep letters, numbers, spaces, hyphens)
- Replace spaces with hyphens
- Remove multiple consecutive hyphens
- Trim leading/trailing hyphens

### Category Normalization
- Split by commas
- Trim whitespace
- Capitalize first letter, lowercase rest
- Remove duplicates
- Filter empty values

### Credit Object
- Default author to "Anonymous" if not provided
- Validate source URLs (remove invalid ones)
- Use current date if not provided
- Format date as YYYY-MM-DD

## 🔗 Integration Points

The system integrates with:
- **Blog Editor** - Automatic metadata generation
- **Blog API** - Server-side processing
- **Category Management** - Existing category system
- **Slug Validation** - Existing slug uniqueness checks

## 📝 Notes

- All functions are pure and side-effect free
- URL validation uses native URL constructor
- Date formatting uses ISO string format
- Component is fully accessible and responsive
- API includes proper error handling and validation
