"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Loader2, Wand2, Copy, Check, AlertCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { generateBlogMetadata, BlogContentInput, BlogMetadataOutput } from "@/lib/action/blog.utils";

interface MetadataProcessorProps {
  title: string;
  content: string;
  onMetadataGenerated?: (metadata: BlogMetadataOutput) => void;
  onSlugChange?: (slug: string) => void;
  onCategoriesChange?: (categories: string[]) => void;
  className?: string;
}

export function MetadataProcessor({
  title,
  content,
  onMetadataGenerated,
  onSlugChange,
  onCategoriesChange,
  className = ""
}: MetadataProcessorProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [metadata, setMetadata] = useState<BlogMetadataOutput | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  
  // Form inputs for manual metadata entry
  const [manualInput, setManualInput] = useState({
    author: "",
    source: "",
    categories: "",
    date: ""
  });

  // Auto-generate metadata when title or content changes
  useEffect(() => {
    if (title.length >= 5 && content.length >= 10) {
      handleAutoGenerate();
    }
  }, [title, content]);

  const handleAutoGenerate = () => {
    if (!title || !content) {
      toast({
        title: "Missing Information",
        description: "Please provide both title and content to generate metadata.",
        variant: "destructive",
      });
      return;
    }

    const input: BlogContentInput = {
      title,
      content,
      author: manualInput.author || undefined,
      source: manualInput.source || undefined,
      categories: manualInput.categories || undefined,
      date: manualInput.date || undefined,
    };

    const generatedMetadata = generateBlogMetadata(input);
    setMetadata(generatedMetadata);
    
    // Notify parent components
    onMetadataGenerated?.(generatedMetadata);
    onSlugChange?.(generatedMetadata.slug);
    onCategoriesChange?.(generatedMetadata.categories);
  };

  const handleProcessWithAPI = async () => {
    if (!title || !content) {
      toast({
        title: "Missing Information",
        description: "Please provide both title and content to process.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const response = await fetch("/api/blog/process-content", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title,
          content,
          author: manualInput.author || undefined,
          source: manualInput.source || undefined,
          categories: manualInput.categories || undefined,
          date: manualInput.date || undefined,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to process content");
      }

      setMetadata(result.data);
      onMetadataGenerated?.(result.data);
      onSlugChange?.(result.data.slug);
      onCategoriesChange?.(result.data.categories);

      toast({
        title: "Success",
        description: "Blog metadata generated successfully!",
      });

    } catch (error) {
      console.error("Error processing content:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process content",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
      
      toast({
        title: "Copied",
        description: `${field} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-primary" />
          Blog Metadata Generator
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Manual Input Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="author">Author (Optional)</Label>
            <Input
              id="author"
              value={manualInput.author}
              onChange={(e) => setManualInput(prev => ({ ...prev, author: e.target.value }))}
              placeholder="Enter author name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="source">Source URL (Optional)</Label>
            <Input
              id="source"
              value={manualInput.source}
              onChange={(e) => setManualInput(prev => ({ ...prev, source: e.target.value }))}
              placeholder="https://example.com/source"
              type="url"
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="categories">Categories (Optional)</Label>
          <Input
            id="categories"
            value={manualInput.categories}
            onChange={(e) => setManualInput(prev => ({ ...prev, categories: e.target.value }))}
            placeholder="ai, technology, productivity"
          />
          <p className="text-xs text-muted-foreground">
            Separate multiple categories with commas
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={handleAutoGenerate}
            variant="outline"
            className="flex-1"
            disabled={!title || !content}
          >
            <Wand2 className="h-4 w-4 mr-2" />
            Auto Generate
          </Button>
          
          <Button
            onClick={handleProcessWithAPI}
            disabled={isProcessing || !title || !content}
            className="flex-1"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Wand2 className="h-4 w-4 mr-2" />
            )}
            Process with API
          </Button>
        </div>

        {/* Generated Metadata Preview */}
        {metadata && (
          <div className="mt-6 p-4 bg-muted/50 rounded-lg space-y-4">
            <h4 className="font-semibold text-sm">Generated Metadata:</h4>
            
            {/* Title */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Title</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(metadata.title, "Title")}
                  className="h-6 w-6 p-0"
                >
                  {copiedField === "Title" ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <p className="text-sm bg-background p-2 rounded border">{metadata.title}</p>
            </div>

            {/* Slug */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Slug</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(metadata.slug, "Slug")}
                  className="h-6 w-6 p-0"
                >
                  {copiedField === "Slug" ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <p className="text-sm bg-background p-2 rounded border font-mono">{metadata.slug}</p>
            </div>

            {/* Categories */}
            {metadata.categories.length > 0 && (
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <Label className="text-xs font-medium">Categories</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(metadata.categories.join(", "), "Categories")}
                    className="h-6 w-6 p-0"
                  >
                    {copiedField === "Categories" ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      <Copy className="h-3 w-3" />
                    )}
                  </Button>
                </div>
                <div className="flex flex-wrap gap-1">
                  {metadata.categories.map((category, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Credit Object */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Credit Information</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(JSON.stringify(metadata.credit, null, 2), "Credit")}
                  className="h-6 w-6 p-0"
                >
                  {copiedField === "Credit" ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <div className="text-sm bg-background p-2 rounded border space-y-1">
                <div><strong>Author:</strong> {metadata.credit.author}</div>
                {metadata.credit.source && (
                  <div><strong>Source:</strong> {metadata.credit.source}</div>
                )}
                <div><strong>Date:</strong> {metadata.credit.date}</div>
              </div>
            </div>

            {/* JSON Output */}
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">JSON Output</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => copyToClipboard(JSON.stringify(metadata, null, 2), "JSON")}
                  className="h-6 w-6 p-0"
                >
                  {copiedField === "JSON" ? (
                    <Check className="h-3 w-3" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <pre className="text-xs bg-background p-2 rounded border overflow-x-auto">
                {JSON.stringify(metadata, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Help Text */}
        <div className="flex items-start gap-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="text-xs text-blue-700 dark:text-blue-300">
            <p className="font-medium mb-1">How it works:</p>
            <ul className="space-y-1 list-disc list-inside">
              <li>Auto Generate: Uses client-side processing for instant results</li>
              <li>Process with API: Uses server-side validation and processing</li>
              <li>Generated slug is URL-safe and SEO-friendly</li>
              <li>Categories are normalized and deduplicated</li>
              <li>Credit object includes author, source, and date information</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
