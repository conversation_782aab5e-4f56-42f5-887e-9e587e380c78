"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Copy, Check, Wand2, FileText, Code } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { generateBlogMetadata, BlogContentInput, BlogMetadataOutput } from "@/lib/action/blog.utils";
import { MetadataProcessor } from "@/components/blog/editor/MetadataProcessor";

export default function MetadataDemoPage() {
  const [input, setInput] = useState<BlogContentInput>({
    title: "The Rise of AI Tools in Everyday Life",
    content: "A deep dive into how ChatGPT, Midjourney, and others are shaping our world. Artificial intelligence has become increasingly integrated into our daily routines, transforming how we work, create, and communicate. From automated customer service to creative content generation, AI tools are revolutionizing various industries and personal workflows.",
    author: "<PERSON>",
    source: "https://example.com/ai-rise",
    categories: "ai, future, productivity, ai",
  });

  const [metadata, setMetadata] = useState<BlogMetadataOutput | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleGenerate = () => {
    if (!input.title || !input.content) {
      toast({
        title: "Missing Information",
        description: "Please provide both title and content.",
        variant: "destructive",
      });
      return;
    }

    const result = generateBlogMetadata(input);
    setMetadata(result);
    
    toast({
      title: "Success",
      description: "Blog metadata generated successfully!",
    });
  };

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
      
      toast({
        title: "Copied",
        description: `${field} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };

  const exampleInputs = [
    {
      title: "The Rise of AI Tools in Everyday Life",
      content: "A deep dive into how ChatGPT, Midjourney, and others are shaping our world.",
      author: "Sarah Khan",
      source: "https://example.com/ai-rise",
      categories: "ai, future, productivity, ai",
    },
    {
      title: "Complete Guide to React Hooks",
      content: "Learn everything about React Hooks including useState, useEffect, and custom hooks with practical examples.",
      author: "John Developer",
      source: "https://reactguide.com/hooks",
      categories: "react, javascript, web development, programming",
    },
    {
      title: "Sustainable Living Tips for 2024",
      content: "Discover practical ways to reduce your carbon footprint and live more sustainably in the modern world.",
      author: "Green Living Team",
      source: "",
      categories: "sustainability, environment, lifestyle",
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Blog Metadata Generator Demo</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Generate SEO-friendly slugs, normalize categories, and create credit objects from blog content.
          This tool follows the exact format specified in your prompt.
        </p>
      </div>

      {/* Quick Examples */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Quick Examples
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {exampleInputs.map((example, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-4 text-left justify-start"
                onClick={() => setInput(example)}
              >
                <div className="space-y-1">
                  <div className="font-medium text-sm">{example.title}</div>
                  <div className="text-xs text-muted-foreground">
                    By {example.author}
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {example.categories.split(',').slice(0, 2).map((cat, i) => (
                      <Badge key={i} variant="secondary" className="text-xs">
                        {cat.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Input Data
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={input.title}
                onChange={(e) => setInput(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter blog title"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content *</Label>
              <Textarea
                id="content"
                value={input.content}
                onChange={(e) => setInput(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter main content"
                className="min-h-[120px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="author">Author (Optional)</Label>
              <Input
                id="author"
                value={input.author || ''}
                onChange={(e) => setInput(prev => ({ ...prev, author: e.target.value }))}
                placeholder="Enter author name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="source">Source URL (Optional)</Label>
              <Input
                id="source"
                value={input.source || ''}
                onChange={(e) => setInput(prev => ({ ...prev, source: e.target.value }))}
                placeholder="https://example.com/source"
                type="url"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="categories">Categories (Optional)</Label>
              <Input
                id="categories"
                value={input.categories || ''}
                onChange={(e) => setInput(prev => ({ ...prev, categories: e.target.value }))}
                placeholder="ai, technology, productivity"
              />
              <p className="text-xs text-muted-foreground">
                Separate multiple categories with commas
              </p>
            </div>

            <Button onClick={handleGenerate} className="w-full">
              <Wand2 className="h-4 w-4 mr-2" />
              Generate Metadata
            </Button>
          </CardContent>
        </Card>

        {/* Output Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Generated Output
            </CardTitle>
          </CardHeader>
          <CardContent>
            {metadata ? (
              <div className="space-y-4">
                {/* JSON Output */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">JSON Output</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(JSON.stringify(metadata, null, 2), "JSON")}
                    >
                      {copiedField === "JSON" ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <pre className="text-sm bg-muted p-4 rounded-lg overflow-x-auto">
                    {JSON.stringify(metadata, null, 2)}
                  </pre>
                </div>

                {/* Individual Fields */}
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Title</Label>
                    <p className="text-sm bg-background p-2 rounded border mt-1">
                      {metadata.title}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Slug</Label>
                    <p className="text-sm bg-background p-2 rounded border mt-1 font-mono">
                      {metadata.slug}
                    </p>
                  </div>

                  {metadata.categories.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium">Categories</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {metadata.categories.map((category, index) => (
                          <Badge key={index} variant="secondary">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div>
                    <Label className="text-sm font-medium">Credit Information</Label>
                    <div className="text-sm bg-background p-2 rounded border mt-1 space-y-1">
                      <div><strong>Author:</strong> {metadata.credit.author}</div>
                      {metadata.credit.source && (
                        <div><strong>Source:</strong> {metadata.credit.source}</div>
                      )}
                      <div><strong>Date:</strong> {metadata.credit.date}</div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Generate metadata to see the output here</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Integrated Component Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Integrated MetadataProcessor Component</CardTitle>
          <p className="text-sm text-muted-foreground">
            This is the same component used in the blog editor sidebar
          </p>
        </CardHeader>
        <CardContent>
          <MetadataProcessor
            title={input.title}
            content={input.content}
            onMetadataGenerated={(metadata) => {
              setMetadata(metadata);
              toast({
                title: "Metadata Generated",
                description: "Metadata has been generated using the integrated component!",
              });
            }}
          />
        </CardContent>
      </Card>

      {/* API Information */}
      <Card>
        <CardHeader>
          <CardTitle>API Endpoint</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            You can also use the API endpoint to process blog content:
          </p>
          <div className="bg-muted p-4 rounded-lg">
            <code className="text-sm">
              POST /api/blog/process-content
            </code>
          </div>
          <p className="text-xs text-muted-foreground">
            Send the same input format and receive the processed metadata as JSON.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
